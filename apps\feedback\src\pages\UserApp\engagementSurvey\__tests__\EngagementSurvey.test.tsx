import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router';
import { vi } from 'vitest';
import EngagementSurvey from '../EngagementSurvey';
import * as engagementSurveyService from '@/services/engagementSurveyService';

// Mock the service
vi.mock('@/services/engagementSurveyService');

// Mock the hooks
vi.mock('../hooks/useSurveyData');
vi.mock('../hooks/useSurveyAutoSave');
vi.mock('../hooks/useSurveyNavigation');

// Mock the components
vi.mock('../components/SurveyHeader', () => ({
  default: ({ surveyData }: any) => <div data-testid="survey-header">{surveyData?.meta?.title}</div>
}));

vi.mock('../components/SurveySidebar', () => ({
  default: ({ surveyData }: any) => <div data-testid="survey-sidebar">Sidebar</div>
}));

vi.mock('../components/SurveyContent', () => ({
  default: ({ surveyData }: any) => <div data-testid="survey-content">Content</div>
}));

vi.mock('../components/SubmitConfirmation', () => ({
  default: ({ onSubmit }: any) => (
    <div data-testid="submit-confirmation">
      <button onClick={onSubmit}>Submit</button>
    </div>
  )
}));

const mockSurveyData = {
  meta: {
    title: 'Test Survey',
    endDate: '2024-12-31',
    canSubmit: true,
    nextTitle: 'Next',
    buttonType: 'primary',
    hideBack: false
  },
  sections: [
    {
      id: 1,
      title: 'Section 1',
      questions: []
    }
  ],
  questions: [],
  responseId: 'response-1',
  versionId: 'version-1',
  completion: 50
};

describe('EngagementSurvey', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state initially', () => {
    const mockUseSurveyData = vi.fn(() => ({
      isLoading: true,
      surveyData: null,
      currentSection: null,
      error: null,
      setCurrentSection: vi.fn(),
      setError: vi.fn(),
      fetchSurveyData: vi.fn(),
      refetchSurveyData: vi.fn()
    }));

    vi.mocked(require('../hooks/useSurveyData')).useSurveyData = mockUseSurveyData;

    render(
      <BrowserRouter>
        <EngagementSurvey />
      </BrowserRouter>
    );

    expect(screen.getByTestId('survey-header')).toBeInTheDocument();
    expect(screen.getByTestId('survey-sidebar')).toBeInTheDocument();
    expect(screen.getByTestId('survey-content')).toBeInTheDocument();
  });

  it('renders error state when there is an error', () => {
    const mockUseSurveyData = vi.fn(() => ({
      isLoading: false,
      surveyData: null,
      currentSection: null,
      error: 'Failed to load survey',
      setCurrentSection: vi.fn(),
      setError: vi.fn(),
      fetchSurveyData: vi.fn(),
      refetchSurveyData: vi.fn()
    }));

    vi.mocked(require('../hooks/useSurveyData')).useSurveyData = mockUseSurveyData;

    render(
      <BrowserRouter>
        <EngagementSurvey />
      </BrowserRouter>
    );

    expect(screen.getByText('Failed to load survey')).toBeInTheDocument();
  });

  it('renders submit confirmation when confirmSubmit is true', () => {
    const mockUseSurveyNavigation = vi.fn(() => ({
      confirmSubmit: true,
      isSubmitting: false,
      setConfirmSubmit: vi.fn(),
      handleNext: vi.fn(),
      handleBack: vi.fn(),
      handleSubmit: vi.fn(),
      handleSectionSelect: vi.fn()
    }));

    vi.mocked(require('../hooks/useSurveyNavigation')).useSurveyNavigation = mockUseSurveyNavigation;

    render(
      <BrowserRouter>
        <EngagementSurvey />
      </BrowserRouter>
    );

    expect(screen.getByTestId('submit-confirmation')).toBeInTheDocument();
  });
});

// TODO: Add more comprehensive tests for:
// - Component interactions
// - Hook integrations
// - Error handling
// - Navigation flows
// - Auto-save functionality
