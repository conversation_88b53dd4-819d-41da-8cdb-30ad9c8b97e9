import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader } from '@repo/ui/components/card';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Menu } from 'lucide-react';
import { USER_ROUTES } from '@/app.routes';
import QuestionRenderer from './components/QuestionRenderer';
import SubmitConfirmation from './components/SubmitConfirmation';
import SurveyHeader from './components/SurveyHeader';
import SurveySidebar from './components/SurveySidebar';
import { useSurveyData } from './hooks/useSurveyData';

const TakeSurvey: React.FC = () => {
  const { id, surveyId } = useParams<{ id: string; surveyId: string }>();
  const navigate = useNavigate();

  // Use custom hook for survey data management
  const {
    isLoading,
    isLoadingQuestions,
    isSaving,
    error,
    meta,
    sections,
    currentSection,
    questions,
    analytics,
    faqs,
    fetchSurveyData,
    loadQuestionsForSection,
    updateQuestionResponse,
    submitSurvey,
    setCurrentSection,
    setError,
    getCompletionPercentage
  } = useSurveyData();

  // Local state for UI
  const [confirmSubmit, setConfirmSubmit] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showFaqDialog, setShowFaqDialog] = useState(false);

  // Initialize survey data on component mount
  useEffect(() => {
    if (id && surveyId) {
      fetchSurveyData(id, surveyId);
    }
  }, [id, surveyId, fetchSurveyData]);

  // Handle section selection
  const handleSectionSelect = async (section: any) => {
    setCurrentSection(section);
    setConfirmSubmit(false);
    await loadQuestionsForSection(section.id);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle navigation
  const handleNext = () => {
    if (!currentSection || !sections) return;

    const currentIndex = sections.findIndex(s => s.id === currentSection.id);
    if (currentIndex < sections.length - 1) {
      setCurrentSection(sections[currentIndex + 1]);
    } else if (meta?.canSubmit) {
      setConfirmSubmit(true);
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBack = () => {
    navigate(USER_ROUTES().dashboard.upwardReview.getPairingsUrl(meta?.indexId || ''));
  };

  // Handle survey submission
  const handleSubmit = async () => {
    if (!id) return;

    try {
      await submitSurvey(id);
      navigate(USER_ROUTES().dashboard.upwardReview.getPairingsUrl(meta?.indexId || ''));
    } catch (err) {
      // Error is already handled in the hook
    }
  };

  // Handle question updates using the hook
  const handleQuestionUpdate = async (questionId: number, response: any, feedback?: string) => {
    await updateQuestionResponse(questionId, response, feedback);
  };

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Submit confirmation state
  if (confirmSubmit) {
    return (
      <SubmitConfirmation
        meta={meta}
        analytics={analytics}
        onBack={() => setConfirmSubmit(false)}
        onSubmit={handleSubmit}
        isLoading={isSaving}
      />
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setShowSidebar(true)}
          className="bg-background/80 backdrop-blur-sm"
        >
          <Menu className="h-4 w-4" />
        </Button>
      </div>

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-40 w-80 bg-background border-r transform transition-transform duration-300 ease-in-out
        ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
      `}>
        <SurveySidebar
          meta={meta}
          sections={sections}
          currentSection={currentSection}
          analytics={analytics}
          faqs={faqs}
          isLoading={isLoading}
          isLoadingQuestions={isLoadingQuestions}
          showFaqDialog={showFaqDialog}
          onSectionSelect={handleSectionSelect}
          onBack={handleBack}
          onNext={handleNext}
          onFaqDialogChange={setShowFaqDialog}
          getCompletionPercentage={getCompletionPercentage}
        />
      </div>

      {/* Overlay for mobile */}
      {showSidebar && (
        <div
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={() => setShowSidebar(false)}
        />
      )}

      {/* Main Content */}
      <div className="lg:ml-80">
        {/* Header */}
        <SurveyHeader
          meta={meta}
          currentSection={currentSection}
          isLoading={isLoading}
          isSaving={isSaving}
          isLoadingQuestions={isLoadingQuestions}
          getCompletionPercentage={getCompletionPercentage}
        />

        {/* Questions */}
        <div className="p-6 space-y-6">
          {isLoading ? (
            // Only show skeleton loaders on initial page load
            Array(3).fill(0).map((_, index) => (
              <Card key={index}>
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-32 w-full" />
                </CardContent>
              </Card>
            ))
          ) : (
            <>
              {isLoadingQuestions && (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">Loading questions...</p>
                </div>
              )}
              {questions.map((question, index) => (
                <QuestionRenderer
                  key={question.id}
                  question={question}
                  questionNumber={index + 1}
                  onUpdate={(response, feedback) => handleQuestionUpdate(question.id, response, feedback)}
                  isSaving={isSaving || isLoadingQuestions}
                />
              ))}
            </>
          )}
        </div>


      </div>
    </div>
  );
};

export default TakeSurvey;
