import axiosInstance from '@/lib/axios';

export interface SurveyMeta {
  title: string;
  surveyFor: string;
  endDate: string;
  lastModified: string;
  canSubmit: boolean;
  nextTitle: string;
  buttonType: string;
  hideBack: boolean;
  indexId: string;
}

export interface Section {
  id: number;
  title: string;
  completed: boolean;
}

export interface Question {
  id: number;
  title: string;
  type: string;
  feedback?: string;
  hasFeedback: boolean;
  options?: any[];
  response?: any;
  categoryID?: number;
  isMandatory?: boolean;
  hasSwap?: boolean;
}

export interface Analytics {
  total: number;
  completed: number;
  sections: number[];
}

export interface SurveyData {
  meta: SurveyMeta;
  sections: Section[];
  questions: Question[];
  analytics: Analytics;
}

// Transform API response to match the expected format
const transformSurveyResponse = (responsesData: any, surveyData: any): SurveyData => {
  const { first_name, last_name } = responsesData?.pairing_detail?.target || {};
  
  // Extract sections from survey data
  const sections = surveyData.sections?.map((section: any) => ({
    id: section.id,
    title: section.name || section.title,
    completed: false // Will be calculated based on responses
  })) || [];

  // Extract questions from survey data
  const questions = surveyData.components?.map((item: any) => {
    const response = responsesData.question_responses?.find((resp: any) => resp.question === item.id);
    
    return {
      id: item.id,
      title: item.label,
      type: item.resourcetype,
      categoryID: item.section,
      isMandatory: item.mandatory,
      hasFeedback: item.collect_feedback,
      hasSwap: item.is_reverse_scale,
      options: item.options || [],
      response: response?.value,
      feedback: responsesData.comment_responses?.find((comment: any) => comment.question === item.id)?.value
    };
  }) || [];

  // Calculate analytics
  const completedQuestions = questions.filter(q => 
    q.response !== undefined && q.response !== null && q.response !== ''
  ).length;

  const completedSections = sections.filter(section => {
    const sectionQuestions = questions.filter(q => q.categoryID === section.id);
    return sectionQuestions.length > 0 && sectionQuestions.every(q => 
      q.response !== undefined && q.response !== null && q.response !== ''
    );
  }).map(s => s.id);

  return {
    meta: {
      title: responsesData.index?.title || 'Survey',
      surveyFor: first_name && last_name ? `${first_name} ${last_name}` : 'Unknown',
      endDate: responsesData.index?.deadline || '',
      lastModified: responsesData.updated_on || 'Just now',
      canSubmit: false, // Will be determined by completion status
      nextTitle: 'Next',
      buttonType: 'primary',
      hideBack: false,
      indexId: responsesData.index?.id || ''
    },
    sections: sections.map(section => ({
      ...section,
      completed: completedSections.includes(section.id)
    })),
    questions,
    analytics: {
      total: questions.length,
      completed: completedQuestions,
      sections: completedSections
    }
  };
};

export const upwardReviewService = {
  // Get survey response data
  getSurveyResponse: async (responseId: string): Promise<any> => {
    const response = await axiosInstance.get(`/survey/survey-response/${responseId}`);
    return response.data;
  },

  // Get survey structure data
  getSurveyData: async (surveyId: string): Promise<any> => {
    const response = await axiosInstance.get(`/survey/survey/${surveyId}`, {
      params: { survey: surveyId }
    });
    return response.data;
  },

  // Get complete survey data (combines response and structure)
  getCompleteSurveyData: async (responseId: string, surveyId: string): Promise<SurveyData> => {
    const [responsesData, surveyData] = await Promise.all([
      upwardReviewService.getSurveyResponse(responseId),
      upwardReviewService.getSurveyData(surveyId)
    ]);

    return transformSurveyResponse(responsesData, surveyData);
  },

  // Get FAQs for a survey
  getFAQs: async (surveyIndex: string): Promise<any[]> => {
    try {
      const response = await axiosInstance.get('/survey/faq/', {
        params: { survey_index: surveyIndex }
      });
      return response.data || [];
    } catch (error) {
      console.warn('Failed to fetch FAQs:', error);
      return [];
    }
  },

  // Update question response
  updateQuestionResponse: async (responseId: string, questionId: number, response: any, feedback?: string): Promise<void> => {
    await axiosInstance.patch(`/survey/response/${responseId}/question/${questionId}/`, {
      response,
      feedback
    });
  },

  // Submit survey
  submitSurvey: async (responseId: string): Promise<void> => {
    await axiosInstance.post(`/survey/response/${responseId}/submit/`);
  }
};
