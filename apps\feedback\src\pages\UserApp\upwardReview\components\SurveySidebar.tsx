import React from 'react';
import { Button } from '@repo/ui/components/button';
import { Badge } from '@repo/ui/components/badge';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/ui/components/dialog';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { Separator } from '@repo/ui/components/separator';
import { Progress } from '@repo/ui/components/progress';
import { ArrowLeft, HelpCircle } from 'lucide-react';
import { SurveyMeta, Section, Analytics } from '@/services/upwardReviewService';

interface SurveySidebarProps {
  meta: SurveyMeta | null;
  sections: Section[];
  currentSection: Section | null;
  analytics: Analytics;
  faqs: any[];
  isLoading: boolean;
  isLoadingQuestions: boolean;
  showFaqDialog: boolean;
  onSectionSelect: (section: Section) => void;
  onBack: () => void;
  onNext: () => void;
  onFaqDialogChange: (open: boolean) => void;
  getCompletionPercentage: () => number;
}

const SurveySidebar: React.FC<SurveySidebarProps> = ({
  meta,
  sections,
  currentSection,
  analytics,
  faqs,
  isLoading,
  isLoadingQuestions,
  showFaqDialog,
  onSectionSelect,
  onBack,
  onNext,
  onFaqDialogChange,
  getCompletionPercentage
}) => {
  return (
    <ScrollArea className="h-full">
      <div className="p-6 space-y-6">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={onBack}
          className="w-full justify-start"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Pairings
        </Button>

        <Separator />

        {/* Navigation Buttons */}
        <div className="space-y-2">
          <Button
            variant="outline"
            onClick={() => {
              if (!currentSection || !sections) return;
              const currentIndex = sections.findIndex(s => s.id === currentSection.id);
              if (currentIndex > 0) {
                onSectionSelect(sections[currentIndex - 1]);
              }
            }}
            disabled={meta?.hideBack || sections.findIndex(s => s.id === currentSection?.id) === 0 || isLoadingQuestions}
            className="w-full justify-start"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous Section
          </Button>
          <Button
            onClick={onNext}
            disabled={isLoadingQuestions}
            className="w-full justify-start"
          >
            {meta?.nextTitle || 'Next Section'}
          </Button>
        </div>

        <Separator />

        {/* Survey Info */}
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-sm text-muted-foreground">Survey Ends On</h3>
            <p className="font-medium">
              {isLoading ? <Skeleton className="h-4 w-32" /> : meta?.endDate}
            </p>
          </div>

          {faqs.length > 0 && (
            <Dialog open={showFaqDialog} onOpenChange={onFaqDialogChange}>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full">
                  <HelpCircle className="mr-2 h-4 w-4" />
                  FAQs
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[80vh]">
                <DialogHeader>
                  <DialogTitle>Frequently Asked Questions</DialogTitle>
                </DialogHeader>
                <ScrollArea className="max-h-[60vh]">
                  <div className="space-y-4">
                    {faqs.map((faq, index) => (
                      <div key={index} className="space-y-2">
                        <h4 className="font-medium">{faq.question}</h4>
                        <p className="text-sm text-muted-foreground">{faq.answer}</p>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </DialogContent>
            </Dialog>
          )}
        </div>

        <Separator />

        {/* Feedback For */}
        <div className="space-y-2">
          <h3 className="font-semibold text-sm text-muted-foreground">Feedback For</h3>
          <h2 className="font-semibold text-lg">
            {isLoading ? <Skeleton className="h-6 w-40" /> : meta?.surveyFor}
          </h2>
        </div>

        {/* Progress */}
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span>Questions</span>
            <span>{analytics.total}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Completed</span>
            <span className="text-green-600">{analytics.completed}</span>
          </div>
          <Progress value={getCompletionPercentage()} className="h-2" />
        </div>

        <Separator />

        {/* Categories */}
        <div className="space-y-3">
          <h3 className="font-semibold text-sm text-muted-foreground">Categories</h3>
          <div className="space-y-1">
            {isLoading ? (
              Array(4).fill(0).map((_, index) => (
                <Skeleton key={index} className="h-10 w-full" />
              ))
            ) : (
              sections.map((section, index) => (
                <div key={section.id}>
                  <Button
                    variant={currentSection?.id === section.id ? 'default' : 'ghost'}
                    className="w-full justify-between"
                    onClick={() => onSectionSelect(section)}
                    disabled={isLoadingQuestions}
                  >
                    <span>{section.title}</span>
                    {analytics.sections.includes(section.id) && (
                      <Badge variant="secondary" className="ml-2">✓</Badge>
                    )}
                  </Button>
                  {index < sections.length - 1 && (
                    <Separator className="my-2" />
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </ScrollArea>
  );
};

export default SurveySidebar;
