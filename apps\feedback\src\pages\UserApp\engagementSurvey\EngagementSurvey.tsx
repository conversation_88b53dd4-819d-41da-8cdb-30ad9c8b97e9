import React from 'react';
import { useParams } from 'react-router';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import SurveyHeader from './components/SurveyHeader';
import SurveySidebar from './components/SurveySidebar';
import SurveyContent from './components/SurveyContent';
import SubmitConfirmation from './components/SubmitConfirmation';
import { useSurveyData } from './hooks/useSurveyData';
import { useSurveyNavigation } from './hooks/useSurveyNavigation';
import { useSurveyAutoSave } from './hooks/useSurveyAutoSave';

const EngagementSurvey: React.FC = () => {
  const { surveyId } = useParams<{ surveyId: string }>();

  // Custom hooks for data management
  const {
    isLoading,
    surveyData,
    currentSection,
    error,
    setCurrentSection,
    setError,
    fetchSurveyData,
    refetchSurveyData
  } = useSurveyData();

  // Custom hooks for auto-save functionality
  const {
    isSaving,
    lastSaved,
    handleResponseUpdate,
    handleCommentUpdate
  } = useSurveyAutoSave();

  // Custom hooks for navigation
  const {
    confirmSubmit,
    isSubmitting,
    setConfirmSubmit,
    handleNext,
    handleBack,
    handleSubmit,
    handleSectionSelect
  } = useSurveyNavigation();

  // Initialize survey data
  React.useEffect(() => {
    if (surveyId) {
      fetchSurveyData(surveyId);
    }
  }, [surveyId, fetchSurveyData]);

  // Handle response updates with refetch
  const onResponseUpdate = (questionId: string, value: any, responseId?: string) => {
    if (!surveyData) return;
    handleResponseUpdate(surveyData.responseId, questionId, value, responseId, refetchSurveyData);
  };

  // Handle comment updates
  const onCommentUpdate = (questionId: string, comment: string, commentId?: string) => {
    if (!surveyData) return;
    handleCommentUpdate(surveyData.responseId, questionId, comment, commentId);
  };

  // Handle navigation actions
  const onNext = () => handleNext(currentSection, surveyData, setCurrentSection);
  const onBack = () => handleBack(currentSection, surveyData, setCurrentSection);
  const onSectionSelect = (section: any) => handleSectionSelect(section, setCurrentSection);
  const onSubmit = async () => {
    try {
      await handleSubmit(surveyData);
    } catch (err) {
      setError('Failed to submit survey');
    }
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (confirmSubmit) {
    return (
      <SubmitConfirmation
        isSubmitting={isSubmitting}
        onBack={() => setConfirmSubmit(false)}
        onSubmit={onSubmit}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 w-full">
      {/* Header */}
      <SurveyHeader
        isLoading={isLoading}
        isSaving={isSaving}
        surveyData={surveyData}
        lastSaved={lastSaved}
      />

      <div className="flex w-full">
        {/* Sidebar */}
        <SurveySidebar
          isLoading={isLoading}
          surveyData={surveyData}
          currentSection={currentSection}
          onSectionSelect={onSectionSelect}
        />

        {/* Main Content */}
        <SurveyContent
          isLoading={isLoading}
          isSaving={isSaving}
          surveyData={surveyData}
          currentSection={currentSection}
          lastSaved={lastSaved}
          onResponseUpdate={onResponseUpdate}
          onCommentUpdate={onCommentUpdate}
          onNext={onNext}
        />
      </div>
    </div>
  );
};

export default EngagementSurvey;
