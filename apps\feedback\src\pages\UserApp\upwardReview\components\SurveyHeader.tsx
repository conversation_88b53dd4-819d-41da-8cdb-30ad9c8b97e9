import React from 'react';
import { Progress } from '@repo/ui/components/progress';
import { Skeleton } from '@repo/ui/components/skeleton';
import { SurveyMeta, Section } from '@/services/upwardReviewService';

interface SurveyHeaderProps {
  meta: SurveyMeta | null;
  currentSection: Section | null;
  isLoading: boolean;
  isSaving: boolean;
  isLoadingQuestions: boolean;
  getCompletionPercentage: () => number;
}

const SurveyHeader: React.FC<SurveyHeaderProps> = ({
  meta,
  currentSection,
  isLoading,
  isSaving,
  isLoadingQuestions,
  getCompletionPercentage
}) => {
  return (
    <div className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b sticky top-0 z-20">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">
              {isLoading ? <Skeleton className="h-8 w-64" /> : meta?.title}
            </h1>
            <p className="text-sm text-muted-foreground mt-1">
              {isLoading ? <Skeleton className="h-4 w-48" /> : currentSection?.title}
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-muted-foreground">
              {isSaving ? 'Saving...' : isLoadingQuestions ? 'Loading...' : 'Progress'}
            </p>
            <p className="font-semibold">{getCompletionPercentage()}% Complete</p>
          </div>
        </div>
        <Progress value={getCompletionPercentage()} className="mt-4" />
      </div>
    </div>
  );
};

export default SurveyHeader;
